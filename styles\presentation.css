:root{
  --bg:#ffffff;
  --fg:#0f172a;
  --muted:#334155;
  --accent:#2563eb;
  --accent-2:#0ea5e9;
  --card:#f8fafc;
  --border:#e2e8f0;
  --shadow:0 8px 30px rgba(2,6,23,0.08);
  --radius:14px;
}

*{box-sizing:border-box}
html,body{height:100%}
body{
  margin:0;
  background:var(--bg);
  color:var(--fg);
  font-family:"Inter","Cairo",system-ui,-apple-system,Segoe UI,Roboto,Helvetica,Arial,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol",sans-serif;
  font-size:18px;
  line-height:1.6;
  -webkit-font-smoothing:antialiased;
  -moz-osx-font-smoothing:grayscale;
}

@font-face{
  font-family:"Cairo";
  font-style:normal;
  font-weight:400 800;
  font-display:swap;
  src: local("Cairo");
}
@font-face{
  font-family:"Inter";
  font-style:normal;
  font-weight:300 900;
  font-display:swap;
  src: local("Inter");
}

/* Deck wrapper */
.deck{
  width:100%;
  min-height:100vh;
  display:flex;
  flex-direction:column;
}

/* Header with logo and title */
.header{
  display:flex;
  align-items:center;
  justify-content:space-between;
  padding:16px 28px;
  border-bottom:1px solid var(--border);
  background:#fff;
  position:sticky;
  top:0;
  z-index:5;
}
.header .brand{
  display:flex;
  align-items:center;
  gap:14px;
}
.header .brand img{
  width:40px;height:40px;object-fit:contain
}
.header .brand .title{
  font-weight:700;
  letter-spacing:.2px;
}
.header .meta{
  color:var(--muted);
  font-size:14px;
}

/* Slide container */
.slides{
  flex:1;
  width:min(1200px,96vw);
  margin:24px auto 32px;
  display:grid;
  gap:22px;
}

/* Slide card */
.slide{
  background:var(--card);
  border:1px solid var(--border);
  border-radius:var(--radius);
  box-shadow:var(--shadow);
  padding:28px;
}

.slide.title{
  background:linear-gradient(180deg,#ffffff, #f4f7fb);
  border-color:#e8eef7;
}
.slide.section{
  background:linear-gradient(180deg,#ffffff, #f6fbff);
  border-color:#dbeafe;
}
.slide .heading{
  margin:0 0 12px 0;
  font-weight:800;
  line-height:1.2;
  letter-spacing:.2px;
}
.slide.title .heading{
  font-size:44px;
  color:var(--fg);
}
.slide .subheading{
  margin:0 0 24px 0;
  color:var(--muted);
  font-size:18px;
}
.badge{
  display:inline-block;
  padding:6px 12px;
  border-radius:999px;
  background:rgba(37,99,235,.08);
  color:#1d4ed8;
  border:1px solid rgba(29,78,216,.2);
  font-size:14px;
  font-weight:600;
}

/* Content layout */
.grid{
  display:grid;
  grid-template-columns:1.2fr .8fr;
  gap:24px;
}
@media (max-width:900px){
  .grid{grid-template-columns:1fr}
}

.card{
  background:#fff;
  border:1px solid var(--border);
  border-radius:12px;
  padding:18px 18px;
}
.card h3{
  margin:0 0 12px;
  font-size:22px;
}
.card ul{
  margin:10px 0 0 22px;
}
.card ul li{
  margin:6px 0;
}

/* Progressive reveal */
.reveal li{
  opacity:.15;
  transform:translateY(2px);
  transition:opacity .35s ease, transform .35s ease;
}
.reveal li.visible{
  opacity:1;
  transform:none;
}

/* CTA / buttons */
.actions{
  margin-top:12px;
  display:flex;
  gap:10px;
  flex-wrap:wrap;
}
.button{
  background:var(--accent);
  color:#fff;
  border:none;
  border-radius:10px;
  padding:10px 14px;
  font-weight:600;
  cursor:pointer;
  box-shadow:0 2px 10px rgba(37,99,235,.18);
}
.button.secondary{
  background:#fff;
  color:var(--accent);
  border:1px solid #bfdbfe;
}

/* Footer */
.footer{
  display:flex;
  justify-content:space-between;
  align-items:center;
  padding:12px 28px 24px;
  color:var(--muted);
  font-size:14px;
}

/* Quiz/answer */
.answer{
  display:none;
  margin-top:8px;
  padding:10px 12px;
  background:#f0f9ff;
  border:1px solid #bae6fd;
  border-radius:8px;
}

/* Table of contents list */
.toc{
  display:grid;
  grid-template-columns:repeat(2,1fr);
  gap:16px;
}
@media (max-width:800px){.toc{grid-template-columns:1fr}}
.toc a{
  text-decoration:none;
  color:var(--fg);
}
.toc .toc-card{
  background:#fff;
  border:1px solid var(--border);
  border-radius:12px;
  padding:16px;
  transition:transform .15s ease, box-shadow .15s ease;
}
.toc .toc-card:hover{
  transform:translateY(-2px);
  box-shadow:0 8px 24px rgba(2,6,23,0.08);
}

/* Print to PDF */
@media print{
  .header, .footer{position:static}
  .slide{page-break-inside:avoid; break-inside:avoid}
  body{background:#fff}
}
