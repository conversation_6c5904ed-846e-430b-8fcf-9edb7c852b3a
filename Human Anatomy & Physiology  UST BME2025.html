<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Human Anatomy & Physiology | University of Science and technology - College of Engineering -Biomedical Engineering</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .fade-in {
            animation: fadeIn 1s ease-in-out;
        }
        .slide {
            display: none;
        }
        .slide.active {
            display: block;
        }
        .system-card {
            transition: all 0.3s ease;
        }
        .system-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .organelle {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .organelle:hover {
            transform: scale(1.05);
        }
        .bone-diagram path {
            transition: fill 0.3s ease;
        }
        .bone-diagram path:hover {
            fill: #3b82f6;
        }
        #actionPotentialChart {
            width: 100%;
            height: 300px;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">
    <!-- Navigation Controls -->
    <div class="fixed bottom-4 right-4 z-50 flex space-x-2">
        <button id="prevBtn" class="bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition">
            <i class="fas fa-arrow-left"></i>
        </button>
        <button id="nextBtn" class="bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition">
            <i class="fas fa-arrow-right"></i>
        </button>
    </div>

    <!-- Slide Counter -->
    <div class="fixed bottom-4 left-4 z-50 bg-white px-4 py-2 rounded-full shadow-lg">
        <span id="currentSlide">1</span> / <span id="totalSlides">35</span>
    </div>

    <!-- Slides Container -->
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- Slide 1: Title Slide -->
        <div class="slide active fade-in bg-white rounded-xl shadow-xl overflow-hidden mb-8">
            <div class="h-4 bg-gradient-to-r from-blue-500 to-blue-800"></div>
            <div class="p-8 text-center">
                <img src="https://img.icons8.com/color/96/000000/brain.png" class="mx-auto mb-6" alt="Anatomy Icon">
                <h1 class="text-4xl font-bold text-gray-800 mb-4">Human Anatomy & Physiology</h1>
                <h2 class="text-2xl text-blue-600 mb-6">College of Engineering - Biomedical Engineering Department</h2>
                <div class="border-t border-gray-200 pt-6">
                    <p class="text-lg text-gray-600 mb-2">Presented by: <span class="font-semibold">Dr. Mohammed Yagoub Esmail</span></p>
                    <p class="text-sm text-gray-500">University of Science and technology - College of Engineering -Biomedical Engineering @ 2025</p>
                    <p class="text-sm text-gray-500 mt-4"><EMAIL></p>
                    <p class="text-sm text-gray-500">Phone: +249912867327, +966538076790</p>
                </div>
            </div>
        </div>

        <!-- Slide 2: Introduction -->
        <div class="slide fade-in bg-white rounded-xl shadow-xl overflow-hidden mb-8">
            <div class="h-4 bg-gradient-to-r from-blue-500 to-blue-800"></div>
            <div class="p-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">Introduction to Human Anatomy & Physiology</h2>
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-semibold text-blue-600 mb-3">Human Anatomy</h3>
                        <ul class="space-y-2 text-gray-700">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Macroscopic Anatomy: Study of large organs and structures visible to naked eye</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Microscopic anatomy: Study of tissues and cells using microscopes</span>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-blue-600 mb-3">Human Physiology</h3>
                        <ul class="space-y-2 text-gray-700">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Vital Functions: Study of processes that sustain life</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Biochemical reactions: Chemical processes within cells</span>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="mt-8 bg-blue-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-blue-700 mb-2">Main Objectives:</h4>
                    <p class="text-gray-700">Understand body structure and function, study different systems, integrate chemistry and biochemistry knowledge</p>
                </div>
            </div>
        </div>

        <!-- Slide 3: Body Systems Overview -->
        <div class="slide fade-in bg-white rounded-xl shadow-xl overflow-hidden mb-8">
            <div class="h-4 bg-gradient-to-r from-blue-500 to-blue-800"></div>
            <div class="p-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">Major Body Systems</h2>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="system-card bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-bone text-blue-600 mr-2 text-xl"></i>
                            <h3 class="font-semibold text-blue-800">Skeletal System</h3>
                        </div>
                        <p class="text-sm text-gray-700">Bones & joints, supports body, protects organs</p>
                    </div>
                    <div class="system-card bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-running text-blue-600 mr-2 text-xl"></i>
                            <h3 class="font-semibold text-blue-800">Muscular System</h3>
                        </div>
                        <p class="text-sm text-gray-700">Muscles that move bones and enable functions</p>
                    </div>
                    <div class="system-card bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-lungs text-blue-600 mr-2 text-xl"></i>
                            <h3 class="font-semibold text-blue-800">Respiratory System</h3>
                        </div>
                        <p class="text-sm text-gray-700">Lungs & airways, gas exchange (O₂/CO₂)</p>
                    </div>
                    <div class="system-card bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-utensils text-blue-600 mr-2 text-xl"></i>
                            <h3 class="font-semibold text-blue-800">Digestive System</h3>
                        </div>
                        <p class="text-sm text-gray-700">Digests food, absorbs nutrients</p>
                    </div>
                    <div class="system-card bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-heartbeat text-blue-600 mr-2 text-xl"></i>
                            <h3 class="font-semibold text-blue-800">Circulatory System</h3>
                        </div>
                        <p class="text-sm text-gray-700">Heart & blood vessels, transports blood</p>
                    </div>
                    <div class="system-card bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-brain text-blue-600 mr-2 text-xl"></i>
                            <h3 class="font-semibold text-blue-800">Nervous System</h3>
                        </div>
                        <p class="text-sm text-gray-700">Brain, spinal cord, nerves, regulates body</p>
                    </div>
                </div>
                <div class="mt-6 bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
                    <h4 class="font-semibold text-yellow-700 mb-2">Did You Know?</h4>
                    <p class="text-gray-700">The human body contains approximately 206 bones and over 600 muscles working together!</p>
                </div>
            </div>
        </div>

        <!-- Slide 4: Body Organization -->
        <div class="slide fade-in bg-white rounded-xl shadow-xl overflow-hidden mb-8">
            <div class="h-4 bg-gradient-to-r from-blue-500 to-blue-800"></div>
            <div class="p-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">Organization of the Human Body</h2>
                <div class="flex flex-col items-center">
                    <div class="w-full max-w-2xl mb-6">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-700">Simplest</span>
                            <span class="text-sm font-medium text-gray-700">Most Complex</span>
                        </div>
                        <div class="bg-gray-200 rounded-full h-4 w-full">
                            <div class="bg-gradient-to-r from-blue-400 to-blue-600 rounded-full h-4" style="width: 100%"></div>
                        </div>
                    </div>
                    <div class="grid md:grid-cols-3 gap-4 w-full">
                        <div class="bg-white p-4 rounded-lg shadow border border-gray-200">
                            <div class="text-blue-600 text-2xl mb-2"><i class="fas fa-atom"></i></div>
                            <h3 class="font-semibold mb-1">Chemical Level</h3>
                            <p class="text-sm text-gray-600">Atoms → Molecules → Macromolecules</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow border border-gray-200">
                            <div class="text-blue-600 text-2xl mb-2"><i class="fas fa-circle"></i></div>
                            <h3 class="font-semibold mb-1">Cellular Level</h3>
                            <p class="text-sm text-gray-600">Basic unit of life</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow border border-gray-200">
                            <div class="text-blue-600 text-2xl mb-2"><i class="fas fa-layer-group"></i></div>
                            <h3 class="font-semibold mb-1">Tissue Level</h3>
                            <p class="text-sm text-gray-600">Groups of similar cells</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow border border-gray-200">
                            <div class="text-blue-600 text-2xl mb-2"><i class="fas fa-heart"></i></div>
                            <h3 class="font-semibold mb-1">Organ Level</h3>
                            <p class="text-sm text-gray-600">Multiple tissue types</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow border border-gray-200">
                            <div class="text-blue-600 text-2xl mb-2"><i class="fas fa-sitemap"></i></div>
                            <h3 class="font-semibold mb-1">System Level</h3>
                            <p class="text-sm text-gray-600">Related organs</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow border border-gray-200">
                            <div class="text-blue-600 text-2xl mb-2"><i class="fas fa-user"></i></div>
                            <h3 class="font-semibold mb-1">Organism Level</h3>
                            <p class="text-sm text-gray-600">Complete individual</p>
                        </div>
                    </div>
                    <div class="mt-8 w-full">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4">Body Cavities</h3>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-medium text-blue-700 mb-2">Dorsal Cavity</h4>
                                <ul class="space-y-1 text-sm text-gray-700">
                                    <li><i class="fas fa-brain mr-2 text-blue-500"></i> Cranial (brain)</li>
                                    <li><i class="fas fa-spine mr-2 text-blue-500"></i> Spinal (spinal cord)</li>
                                </ul>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-medium text-blue-700 mb-2">Ventral Cavity</h4>
                                <ul class="space-y-1 text-sm text-gray-700">
                                    <li><i class="fas fa-heartbeat mr-2 text-blue-500"></i> Thoracic (heart, lungs)</li>
                                    <li><i class="fas fa-stomach mr-2 text-blue-500"></i> Abdominal (digestive)</li>
                                    <li><i class="fas fa-bladder mr-2 text-blue-500"></i> Pelvic (bladder, reproductive)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 5: Anatomical Terms -->
        <div class="slide fade-in bg-white rounded-xl shadow-xl overflow-hidden mb-8">
            <div class="h-4 bg-gradient-to-r from-blue-500 to-blue-800"></div>
            <div class="p-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">Anatomical Terms & Positions</h2>
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-semibold text-blue-600 mb-3">Anatomical Planes</h3>
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <div class="bg-blue-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-cut text-blue-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">Sagittal Plane</h4>
                                    <p class="text-sm text-gray-600">Divides body into left/right portions</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-blue-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-cut text-blue-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">Frontal (Coronal) Plane</h4>
                                    <p class="text-sm text-gray-600">Divides body into front/back portions</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-blue-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-cut text-blue-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">Transverse Plane</h4>
                                    <p class="text-sm text-gray-600">Divides body into upper/lower portions</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-blue-600 mb-3">Directional Terms</h3>
                        <div class="grid grid-cols-2 gap-3">
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="font-medium">Superior</p>
                                <p class="text-sm text-gray-600">Above/toward head</p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="font-medium">Inferior</p>
                                <p class="text-sm text-gray-600">Below/toward feet</p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="font-medium">Anterior</p>
                                <p class="text-sm text-gray-600">Front of body</p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="font-medium">Posterior</p>
                                <p class="text-sm text-gray-600">Back of body</p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="font-medium">Medial</p>
                                <p class="text-sm text-gray-600">Toward midline</p>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="font-medium">Lateral</p>
                                <p class="text-sm text-gray-600">Away from midline</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-6 bg-white border border-gray-200 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">Standard Anatomical Position</h3>
                    <div class="flex flex-col md:flex-row items-center">
                        <div class="md:w-1/3 mb-4 md:mb-0">
                            <img src="https://img.icons8.com/color/96/000000/standing-man.png" alt="Anatomical Position" class="mx-auto">
                        </div>
                        <div class="md:w-2/3">
                            <ul class="list-disc pl-5 text-gray-700 space-y-1">
                                <li>Standing upright</li>
                                <li>Face forward</li>
                                <li>Arms at sides</li>
                                <li>Palms facing forward</li>
                                <li>Feet parallel</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 6: The Cell -->
        <div class="slide fade-in bg-white rounded-xl shadow-xl overflow-hidden mb-8">
            <div class="h-4 bg-gradient-to-r from-blue-500 to-blue-800"></div>
            <div class="p-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">The Cell: Basic Unit of Life</h2>
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-semibold text-blue-600 mb-3">Cell Components</h3>
                        <div class="relative">
                            <img src="https://img.icons8.com/color/300/000000/animal-cell.png" alt="Animal Cell" class="w-full h-auto rounded-lg border border-gray-200">
                            <div id="organelleTooltip" class="absolute bg-white p-3 rounded-lg shadow-lg hidden" style="min-width: 200px;">
                                <h4 class="font-semibold text-blue-600" id="organelleName">Organelle</h4>
                                <p class="text-sm text-gray-700" id="organelleFunction">Function description</p>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-blue-600 mb-3">Cell Functions</h3>
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <div class="bg-green-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-divide text-green-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">Reproduction</h4>
                                    <p class="text-sm text-gray-600">Cell division (mitosis/meiosis)</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-green-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-exchange-alt text-green-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">Transport</h4>
                                    <p class="text-sm text-gray-600">Movement of nutrients/waste</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-green-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-network-wired text-green-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">Coordination</h4>
                                    <p class="text-sm text-gray-600">Cell signaling & communication</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-green-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-chart-line text-green-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">Growth</h4>
                                    <p class="text-sm text-gray-600">Increase in size/number</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-green-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-specialty text-green-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">Differentiation</h4>
                                    <p class="text-sm text-gray-600">Specialization of cell function</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-6 bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-700 mb-2">Cell Types</h4>
                            <div class="flex space-x-4">
                                <div class="flex-1 bg-white p-3 rounded-lg shadow-sm">
                                    <i class="fas fa-leaf text-green-500 mb-1"></i>
                                    <p class="text-sm font-medium">Plant Cells</p>
                                </div>
                                <div class="flex-1 bg-white p-3 rounded-lg shadow-sm">
                                    <i class="fas fa-dog text-blue-500 mb-1"></i>
                                    <p class="text-sm font-medium">Animal Cells</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 7: Cell Organelles -->
        <div class="slide fade-in bg-white rounded-xl shadow-xl overflow-hidden mb-8">
            <div class="h-4 bg-gradient-to-r from-blue-500 to-blue-800"></div>
            <div class="p-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">Cell Organelles: Structure & Function</h2>
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="organelle bg-white p-4 rounded-lg border border-gray-200 shadow-sm" data-name="Cell Membrane" data-function="Selective barrier, regulates material passage, cell communication">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                <i class="fas fa-shield-alt text-blue-500"></i>
                            </div>
                            <h3 class="font-semibold">Cell Membrane</h3>
                        </div>
                        <p class="text-sm text-gray-600">Phospholipid bilayer with proteins</p>
                    </div>
                    <div class="organelle bg-white p-4 rounded-lg border border-gray-200 shadow-sm" data-name="Nucleus" data-function="Contains DNA, controls cell activities">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-2">
                                <i class="fas fa-dna text-purple-500"></i>
                            </div>
                            <h3 class="font-semibold">Nucleus</h3>
                        </div>
                        <p class="text-sm text-gray-600">Control center with genetic material</p>
                    </div>
                    <div class="organelle bg-white p-4 rounded-lg border border-gray-200 shadow-sm" data-name="Mitochondria" data-function="ATP production, cellular respiration">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-2">
                                <i class="fas fa-bolt text-red-500"></i>
                            </div>
                            <h3 class="font-semibold">Mitochondria</h3>
                        </div>
                        <p class="text-sm text-gray-600">Powerhouse of the cell</p>
                    </div>
                    <div class="organelle bg-white p-4 rounded-lg border border-gray-200 shadow-sm" data-name="Ribosomes" data-function="Protein synthesis">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-2">
                                <i class="fas fa-cogs text-yellow-500"></i>
                            </div>
                            <h3 class="font-semibold">Ribosomes</h3>
                        </div>
                        <p class="text-sm text-gray-600">Protein factories</p>
                    </div>
                    <div class="organelle bg-white p-4 rounded-lg border border-gray-200 shadow-sm" data-name="Endoplasmic Reticulum" data-function="RER: Protein synthesis, SER: Lipid synthesis">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-2">
                                <i class="fas fa-network-wired text-green-500"></i>
                            </div>
                            <h3 class="font-semibold">Endoplasmic Reticulum</h3>
                        </div>
                        <p class="text-sm text-gray-600">Transport & synthesis network</p>
                    </div>
                    <div class="organelle bg-white p-4 rounded-lg border border-gray-200 shadow-sm" data-name="Golgi Apparatus" data-function="Modifies, packages, distributes proteins & lipids">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-2">
                                <i class="fas fa-boxes text-indigo-500"></i>
                            </div>
                            <h3 class="font-semibold">Golgi Apparatus</h3>
                        </div>
                        <p class="text-sm text-gray-600">Post office of the cell</p>
                    </div>
                </div>
                <div class="mt-6 bg-white border border-gray-200 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">Organelle Details</h3>
                    <div id="organelleDetails" class="text-gray-700">
                        <p class="text-center text-gray-500">Click on an organelle to see details</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 8: Cell Membrane -->
        <div class="slide fade-in bg-white rounded-xl shadow-xl overflow-hidden mb-8">
            <div class="h-4 bg-gradient-to-r from-blue-500 to-blue-800"></div>
            <div class="p-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">Cell Membrane Structure</h2>
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <div class="bg-gray-100 p-4 rounded-lg mb-4">
                            <img src="https://img.icons8.com/color/96/000000/cell-membrane.png" class="mx-auto" alt="Cell Membrane">
                        </div>
                        <h3 class="text-xl font-semibold text-blue-600 mb-3">Main Components</h3>
                        <ul class="space-y-3 text-gray-700">
                            <li class="flex items-start">
                                <div class="bg-blue-100 p-1 rounded-full mr-2">
                                    <i class="fas fa-circle text-blue-500 text-xs"></i>
                                </div>
                                <div>
                                    <span class="font-medium">Phospholipids</span>
                                    <p class="text-sm text-gray-600">Bilayer with hydrophilic heads & hydrophobic tails</p>
                                </div>
                            </li>
                            <li class="flex items-start">
                                <div class="bg-purple-100 p-1 rounded-full mr-2">
                                    <i class="fas fa-circle text-purple-500 text-xs"></i>
                                </div>
                                <div>
                                    <span class="font-medium">Proteins</span>
                                    <p class="text-sm text-gray-600">Transport, receptors, structural support</p>
                                </div>
                            </li>
                            <li class="flex items-start">
                                <div class="bg-green-100 p-1 rounded-full mr-2">
                                    <i class="fas fa-circle text-green-500 text-xs"></i>
                                </div>
                                <div>
                                    <span class="font-medium">Carbohydrates</span>
                                    <p class="text-sm text-gray-600">Cell recognition & communication</p>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-blue-600 mb-3">Membrane Functions</h3>
                        <div class="space-y-4">
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                        <i class="fas fa-filter text-blue-500"></i>
                                    </div>
                                    <h4 class="font-medium">Selective Barrier</h4>
                                </div>
                                <p class="text-sm text-gray-600">Regulates passage of substances, maintains homeostasis</p>
                            </div>
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-2">
                                        <i class="fas fa-exchange-alt text-purple-500"></i>
                                    </div>
                                    <h4 class="font-medium">Material Transport</h4>
                                </div>
                                <p class="text-sm text-gray-600">Active & passive transport mechanisms</p>
                            </div>
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-2">
                                        <i class="fas fa-satellite-dish text-green-500"></i>
                                    </div>
                                    <h4 class="font-medium">Signal Recognition</h4>
                                </div>
                                <p class="text-sm text-gray-600">Receptors detect chemical signals</p>
                            </div>
                        </div>
                        <div class="mt-6 bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
                            <h4 class="font-semibold text-yellow-700 mb-2">Fluid Mosaic Model</h4>
                            <p class="text-sm text-gray-700">The cell membrane is dynamic with proteins floating in a phospholipid sea</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 9: Transport Mechanisms -->
        <div class="slide fade-in bg-white rounded-xl shadow-xl overflow-hidden mb-8">
            <div class="h-4 bg-gradient-to-r from-blue-500 to-blue-800"></div>
            <div class="p-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">Cellular Transport Mechanisms</h2>
                <div class="grid md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-semibold text-blue-600 mb-3">Passive Transport</h3>
                        <div class="space-y-4">
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                        <i class="fas fa-random text-blue-500"></i>
                                    </div>
                                    <h4 class="font-medium">Diffusion</h4>
                                </div>
                                <p class="text-sm text-gray-600">Movement from high to low concentration</p>
                            </div>
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-2">
                                        <i class="fas fa-road text-purple-500"></i>
                                    </div>
                                    <h4 class="font-medium">Facilitated Diffusion</h4>
                                </div>
                                <p class="text-sm text-gray-600">Protein-assisted movement (no energy)</p>
                            </div>
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-2">
                                        <i class="fas fa-tint text-green-500"></i>
                                    </div>
                                    <h4 class="font-medium">Osmosis</h4>
                                </div>
                                <p class="text-sm text-gray-600">Water movement across membrane</p>
                            </div>
                        </div>
                        <div class="mt-6 bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-700 mb-2">Passive Transport Features</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li><i class="fas fa-check-circle text-blue-500 mr-2"></i> No energy required (ATP)</li>
                                <li><i class="fas fa-check-circle text-blue-500 mr-2"></i> Moves with concentration gradient</li>
                                <li><i class="fas fa-check-circle text-blue-500 mr-2"></i> Continues until equilibrium</li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-blue-600 mb-3">Active Transport</h3>
                        <div class="space-y-4">
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-2">
                                        <i class="fas fa-battery-full text-red-500"></i>
                                    </div>
                                    <h4 class="font-medium">Primary Active Transport</h4>
                                </div>
                                <p class="text-sm text-gray-600">Direct ATP use (e.g., Na+/K+ pump)</p>
                            </div>
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-2">
                                        <i class="fas fa-exchange-alt text-yellow-500"></i>
                                    </div>
                                    <h4 class="font-medium">Secondary Active Transport</h4>
                                </div>
                                <p class="text-sm text-gray-600">Uses established gradient (co-transport)</p>
                            </div>
                            <div class="bg-white border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-2">
                                        <i class="fas fa-shipping-fast text-indigo-500"></i>
                                    </div>
                                    <h4 class="font-medium">Vesicular Transport</h4>
                                </div>
                                <div class="text-sm text-gray-600">
                                    <p><i class="fas fa-arrow-down text-green-500 mr-1"></i> Endocytosis (into cell)</p>
                                    <p><i class="fas fa-arrow-up text-red-500 mr-1"></i> Exocytosis (out of cell)</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-6 bg-red-50 p
</body>
</html>