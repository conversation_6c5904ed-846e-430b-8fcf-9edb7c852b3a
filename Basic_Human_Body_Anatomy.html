<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Human Body Anatomy | UST BME 2025</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }
        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .fade-in { animation: fadeIn 0.8s ease-out; }
        .slide-in-left { animation: slideInLeft 0.8s ease-out; }
        .slide-in-right { animation: slideInRight 0.8s ease-out; }
        .pulse-animation { animation: pulse 2s infinite; }
        .rotate-animation { animation: rotate 2s linear infinite; }
        
        .slide {
            display: none;
            min-height: 100vh;
        }
        .slide.active {
            display: block;
        }
        
        .interactive-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .interactive-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .system-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .system-icon:hover {
            transform: scale(1.2) rotate(5deg);
        }
        
        .quiz-option {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .quiz-option:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .quiz-option.correct {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
        }
        
        .quiz-option.incorrect {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }

        /* Bilingual support */
        .en {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .ar {
            font-family: 'Traditional Arabic', 'Arial', sans-serif;
            direction: rtl;
        }
    </style>
    <!-- Mermaid and Lottie for diagrams and animated icons -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.2/lottie.min.js"></script>
</head>
<body class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 font-sans">
    <!-- Navigation Controls -->
    <div class="fixed bottom-6 right-6 z-50 flex space-x-3">
        <button id="prevBtn" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110">
            <i class="fas fa-arrow-left text-lg"></i>
        </button>
        <button id="nextBtn" class="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110">
            <i class="fas fa-arrow-right text-lg"></i>
        </button>
    </div>

    <!-- Slide Counter -->
    <div class="fixed bottom-6 left-6 z-50 bg-white/90 backdrop-blur-sm px-6 py-3 rounded-full shadow-lg border border-white/20">
        <span class="text-lg font-semibold text-gray-700">
            <span id="currentSlide">1</span> / <span id="totalSlides">35</span>
        </span>
    </div>

    <!-- Progress Bar -->
    <div class="fixed top-0 left-0 w-full h-1 bg-gray-200 z-50">
        <div id="progressBar" class="h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-300" style="width: 2.85%"></div>
    </div>

    <!-- Slides Container -->
    <div class="container mx-auto px-6 py-8 max-w-7xl">
        
        <!-- Slide 1: Title Slide -->
        <div class="slide active fade-in">
            <div class="min-h-screen flex items-center justify-center">
                <div class="text-center">
                    <div id="dnaAnimation" class="mb-8 mx-auto w-48 h-48"></div>
                    <script>
                        document.addEventListener('DOMContentLoaded', () => {
                            if (window.bodymovin) {
                                bodymovin.loadAnimation({
                                    container: document.getElementById('dnaAnimation'),
                                    renderer: 'svg',
                                    loop: true,
                                    autoplay: true,
                                    path: 'https://assets4.lottiefiles.com/packages/lf20_w51pcehl.json'
                                });
                            }
                        });
                    </script>
                    <h1 class="text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-6 en">
                        Basic Human Body Anatomy
                    </h1>
                    <h1 class="text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-6 ar">
                        أساسيات تشريح جسم الإنسان
                    </h1>
                    <h2 class="text-3xl text-gray-700 mb-8 en">Comprehensive Guide to Human Anatomy</h2>
                    <h2 class="text-3xl text-gray-700 mb-8 ar">دليل شامل لتشريح جسم الإنسان</h2>
                    <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/20 max-w-2xl mx-auto">
                        <p class="text-xl text-gray-600 mb-4">Sudan University of Science & Technology</p>
                        <p class="text-lg text-blue-600 font-semibold mb-2">Biomedical Engineering Department</p>
                        <p class="text-gray-500">Dr. Mohammed Yagoub Esmail | BME 2025</p>
                        <p class="text-gray-500 mt-2"><EMAIL></p>
                        <p class="text-gray-500">Phone: +************, +************</p>
                        <p class="text-gray-400 mt-4 text-sm">© 2025 All Rights Reserved</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2: Course Overview -->
        <div class="slide">
            <div class="min-h-screen flex items-center">
                <div class="w-full">
                    <h2 class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent en">
                        Course Overview
                    </h2>
                    <h2 class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent ar">
                        نظرة عامة على المقرر
                    </h2>
                    <div class="grid md:grid-cols-2 gap-8">
                        <div class="space-y-6 en">
                            <div class="interactive-card bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                                <div class="flex items-center mb-4">
                                    <i class="fas fa-book-medical text-3xl text-blue-600 mr-4"></i>
                                    <h3 class="text-xl font-semibold text-gray-800">Course Modules</h3>
                                </div>
                                <ul class="space-y-3 text-gray-700">
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                        <span>Introduction to Anatomical Terminology</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                        <span>Major Body Systems & Their Functions</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                        <span>Clinical Applications & Biomedical Engineering Relevance</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="space-y-6 ar">
                            <div class="interactive-card bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                                <div class="flex items-center mb-4">
                                    <i class="fas fa-book-medical text-3xl text-blue-600 mr-4"></i>
                                    <h3 class="text-xl font-semibold text-gray-800">وحدات المقرر</h3>
                                </div>
                                <ul class="space-y-3 text-gray-700">
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                        <span>مقدمة في المصطلحات التشريحية</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                        <span>أنظمة الجسم الرئيسية ووظائفها</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                        <span>التطبيقات السريرية وأهميتها للهندسة الطبية الحيوية</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="mt-8 grid md:grid-cols-3 gap-6">
                        <div class="interactive-card bg-gradient-to-r from-blue-500 to-indigo-600 p-6 rounded-2xl text-white">
                            <div class="text-center mb-4">
                                <i class="fas fa-graduation-cap text-4xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold mb-2 text-center en">Learning Outcomes</h3>
                            <h3 class="text-xl font-semibold mb-2 text-center ar">مخرجات التعلم</h3>
                            <p class="text-sm opacity-90 en">Understand anatomical structures and their relationships</p>
                            <p class="text-sm opacity-90 ar">فهم التراكيب التشريحية وعلاقاتها</p>
                        </div>
                        <div class="interactive-card bg-gradient-to-r from-purple-500 to-pink-600 p-6 rounded-2xl text-white">
                            <div class="text-center mb-4">
                                <i class="fas fa-microscope text-4xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold mb-2 text-center en">Practical Applications</h3>
                            <h3 class="text-xl font-semibold mb-2 text-center ar">التطبيقات العملية</h3>
                            <p class="text-sm opacity-90 en">Apply anatomical knowledge to biomedical engineering problems</p>
                            <p class="text-sm opacity-90 ar">تطبيق المعرفة التشريحية على مشاكل الهندسة الطبية الحيوية</p>
                        </div>
                        <div class="interactive-card bg-gradient-to-r from-green-500 to-teal-600 p-6 rounded-2xl text-white">
                            <div class="text-center mb-4">
                                <i class="fas fa-stethoscope text-4xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold mb-2 text-center en">Clinical Relevance</h3>
                            <h3 class="text-xl font-semibold mb-2 text-center ar">الأهمية السريرية</h3>
                            <p class="text-sm opacity-90 en">Connect anatomical structures to clinical conditions</p>
                            <p class="text-sm opacity-90 ar">ربط التراكيب التشريحية بالحالات السريرية</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3: Introduction to Anatomy -->
        <div class="slide">
            <div class="min-h-screen flex items-center">
                <div class="w-full">
                    <h2 class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        <span class="en">Introduction to Anatomy</span>
                        <span class="ar">مقدمة في علم التشريح</span>
                    </h2>
                    <div class="grid md:grid-cols-2 gap-12 items-center">
                        <div class="slide-in-left">
                            <div class="bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-xl border border-white/20">
                                <div class="text-center mb-6">
                                    <i class="fas fa-microscope text-6xl text-blue-600 pulse-animation"></i>
                                </div>
                                <h3 class="text-2xl font-bold text-gray-800 mb-4 text-center en">What is Anatomy?</h3>
                                <h3 class="text-2xl font-bold text-gray-800 mb-4 text-center ar">ما هو علم التشريح؟</h3>
                                <p class="text-lg text-gray-700 leading-relaxed en">
                                    <strong>Anatomy</strong> is the scientific study of the structure and organization of living organisms. 
                                    It examines the physical form, arrangement, and relationships between different parts of the body.
                                </p>
                                <p class="text-lg text-gray-700 leading-relaxed ar">
                                    <strong>علم التشريح</strong> هو الدراسة العلمية لبنية وتنظيم الكائنات الحية.
                                    يدرس الشكل المادي والترتيب والعلاقات بين أجزاء الجسم المختلفة.
                                </p>
                            </div>
                        </div>
                        <div class="slide-in-right">
                            <div class="space-y-6">
                                <div class="interactive-card bg-gradient-to-r from-blue-500 to-purple-600 p-6 rounded-2xl text-white">
                                    <h4 class="text-xl font-semibold mb-3 en">Etymology</h4>
                                    <h4 class="text-xl font-semibold mb-3 ar">أصل الكلمة</h4>
                                    <p class="en">From Greek: <em>ana</em> (up) + <em>tome</em> (cutting)</p>
                                    <p class="ar">من اليونانية: <em>ana</em> (فوق) + <em>tome</em> (قطع)</p>
                                    <p class="text-sm mt-2 opacity-90 en">Originally meaning "to cut up" or dissect</p>
                                    <p class="text-sm mt-2 opacity-90 ar">المعنى الأصلي "للتقطيع" أو التشريح</p>
                                </div>
                                <div class="interactive-card bg-gradient-to-r from-green-500 to-teal-600 p-6 rounded-2xl text-white">
                                    <h4 class="text-xl font-semibold mb-3 en">Types of Anatomy</h4>
                                    <h4 class="text-xl font-semibold mb-3 ar">أنواع علم التشريح</h4>
                                    <ul class="space-y-2 en">
                                        <li><i class="fas fa-arrow-right mr-2"></i> Gross/Macroscopic Anatomy</li>
                                        <li><i class="fas fa-arrow-right mr-2"></i> Microscopic Anatomy (Histology)</li>
                                        <li><i class="fas fa-arrow-right mr-2"></i> Developmental Anatomy</li>
                                    </ul>
                                    <ul class="space-y-2 ar">
                                        <li><i class="fas fa-arrow-right mr-2"></i> التشريح العياني/الكلي</li>
                                        <li><i class="fas fa-arrow-right mr-2"></i> التشريح المجهري (علم الأنسجة)</li>
                                        <li><i class="fas fa-arrow-right mr-2"></i> التشريح التطوري</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: Anatomical Terminology -->
        <div class="slide">
            <div class="min-h-screen flex items-center">
                <div class="w-full">
                    <h2 class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        <span class="en">Anatomical Terminology</span>
                        <span class="ar">المصطلحات التشريحية</span>
                    </h2>
                    <div class="grid md:grid-cols-2 gap-8">
                        <div class="space-y-6">
                            <div class="interactive-card bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                                <div class="flex items-center mb-4">
                                    <i class="fas fa-compass text-3xl text-blue-600 mr-4"></i>
                                    <h3 class="text-xl font-semibold text-gray-800 en">Directional Terms</h3>
                                    <h3 class="text-xl font-semibold text-gray-800 ar">المصطلحات الاتجاهية</h3>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                    <div class="bg-blue-50 p-3 rounded-lg">
                                        <p class="font-semibold text-blue-800 en">Superior/Inferior</p>
                                        <p class="font-semibold text-blue-800 ar">علوي/سفلي</p>
                                        <p class="text-sm text-gray-700 en">Above/below</p>
                                        <p class="text-sm text-gray-700 ar">فوق/تحت</p>
                                    </div>
                                    <div class="bg-blue-50 p-3 rounded-lg">
                                        <p class="font-semibold text-blue-800 en">Anterior/Posterior</p>
                                        <p class="font-semibold text-blue-800 ar">أمامي/خلفي</p>
                                        <p class="text-sm text-gray-700 en">Front/back</p>
                                        <p class="text-sm text-gray-700 ar">الأمام/الخلف</p>
                                    </div>
                                    <div class="bg-blue-50 p-3 rounded-lg">
                                        <p class="font-semibold text-blue-800 en">Medial/Lateral</p>
                                        <p class="font-semibold text-blue-800 ar">إنسي/وحشي</p>
                                        <p class="text-sm text-gray-700 en">Toward/away from midline</p>
                                        <p class="text-sm text-gray-700 ar">نحو/بعيدًا عن خط المنتصف</p>
                                    </div>
                                    <div class="bg-blue-50 p-3 rounded-lg">
                                        <p class="font-semibold text-blue-800 en">Proximal/Distal</p>
                                        <p class="font-semibold text-blue-800 ar">قريب/بعيد</p>
                                        <p class="text-sm text-gray-700 en">Near/far from attachment</p>
                                        <p class="text-sm text-gray-700 ar">قريب/بعيد من نقطة الاتصال</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="space-y-6">
                            <div class="interactive-card bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                                <div class="flex items-center mb-4">
                                    <i class="fas fa-ruler-combined text-3xl text-purple-600 mr-4"></i>
                                    <h3 class="text-xl font-semibold text-gray-800 en">Anatomical Planes</h3>
                                    <h3 class="text-xl font-semibold text-gray-800 ar">المستويات التشريحية</h3>
                                </div>
                                <div class="grid grid-cols-1 gap-3">
                                    <div class="bg-purple-50 p-3 rounded-lg">
                                        <p class="font-semibold text-purple-800 en">Sagittal Plane</p>
                                        <p class="font-semibold text-purple-800 ar">المستوى السهمي</p>
                                        <p class="text-sm text-gray-700 en">Divides body into right and left portions</p>
                                        <p class="text-sm text-gray-700 ar">يقسم الجسم إلى أجزاء يمنى ويسرى</p>
                                    </div>
                                    <div class="bg-purple-50 p-3 rounded-lg">
                                        <p class="font-semibold text-purple-800 en">Coronal/Frontal Plane</p>
                                        <p class="font-semibold text-purple-800 ar">المستوى الإكليلي/الجبهي</p>
                                        <p class="text-sm text-gray-700 en">Divides body into anterior and posterior portions</p>
                                        <p class="text-sm text-gray-700 ar">يقسم الجسم إلى أجزاء أمامية وخلفية</p>
                                    </div>
                                    <div class="bg-purple-50 p-3 rounded-lg">
                                        <p class="font-semibold text-purple-800 en">Transverse/Horizontal Plane</p>
                                        <p class="font-semibold text-purple-800 ar">المستوى المستعرض/الأفقي</p>
                                        <p class="text-sm text-gray-700 en">Divides body into superior and inferior portions</p>
                                        <p class="text-sm text-gray-700 ar">يقسم الجسم إلى أجزاء علوية وسفلية</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-8 bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4 en">Anatomical Position</h3>
                        <h3 class="text-xl font-semibold text-gray-800 mb-4 ar">الوضع التشريحي</h3>
                        <div class="grid md:grid-cols-2 gap-6 items-center">
                            <div>
                                <p class="text-gray-700 mb-4 en">
                                    The standard reference position in anatomy: standing upright, facing forward, arms at sides with palms facing forward, and feet parallel.
                                </p>
                                <p class="text-gray-700 mb-4 ar">
                                    الوضع المرجعي القياسي في التشريح: الوقوف منتصبًا، والنظر للأمام، والذراعين على الجانبين مع توجيه راحة اليدين للأمام، والقدمين متوازيتين.
                                </p>
                            </div>
                            <div class="flex justify-center">
                                <div class="relative w-48 h-64 bg-gradient-to-b from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center">
                                    <div class="w-24 h-48 bg-blue-500/20 rounded-full relative">
                                        <!-- Head -->
                                        <div class="absolute w-8 h-8 bg-blue-500/30 rounded-full top-0 left-1/2 transform -translate-x-1/2"></div>
                                        <!-- Arms -->
                                        <div class="absolute w-20 h-3 bg-blue-500/30 top-12 left-1/2 transform -translate-x-1/2"></div>
                                        <!-- Legs -->
                                        <div class="absolute w-3 h-16 bg-blue-500/30 bottom-0 left-1/3"></div>
                                        <div class="absolute w-3 h-16 bg-blue-500/30 bottom-0 right-1/3"></div>
                                        
                                        <!-- Directional Labels -->
                                        <div class="absolute -top-10 left-1/2 transform -translate-x-1/2 text-blue-800 font-bold">Superior</div>
                                        <div class="absolute -bottom-10 left-1/2 transform -translate-x-1/2 text-blue-800 font-bold">Inferior</div>
                                        <div class="absolute top-1/2 -left-16 transform -translate-y-1/2 text-blue-800 font-bold">Lateral</div>
                                        <div class="absolute top-1/2 -right-16 transform -translate-y-1/2 text-blue-800 font-bold">Lateral</div>
                                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-blue-800 font-bold">Medial</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 5: Body Organization -->
        <div class="slide">
            <!-- Diagrams & Interactives Enhancements -->
            <div class="mb-6 flex items-center justify-center gap-3">
                <button id="toggleLang" class="px-3 py-1.5 rounded-md bg-white/80 border text-sm shadow"><i class="fas fa-language mr-2"></i>EN/AR</button>
                <button id="openHelp" class="px-3 py-1.5 rounded-md bg-white/80 border text-sm shadow"><i class="fas fa-circle-question mr-2"></i>Help</button>
            </div>
            <div id="helpModal" class="fixed inset-0 hidden items-center justify-center bg-black/50 z-50">
                <div class="bg-white rounded-2xl p-6 shadow-2xl max-w-lg w-11/12">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-xl font-semibold">How to Use</h3>
                        <button id="closeHelp" class="text-gray-500 hover:text-gray-700"><i class="fas fa-xmark"></i></button>
                    </div>
                    <ul class="text-sm text-gray-700 space-y-2">
                        <li><i class="fas fa-arrow-right text-blue-600 mr-2"></i> Navigate with arrow keys or buttons.</li>
                        <li><i class="fas fa-arrow-right text-blue-600 mr-2"></i> Click hotspots on images to reveal labels.</li>
                        <li><i class="fas fa-arrow-right text-blue-600 mr-2"></i> Copy Mermaid code from diagram toolbar.</li>
                        <li><i class="fas fa-arrow-right text-blue-600 mr-2"></i> Toggle EN/AR labels with the language button.</li>
                    </ul>
                </div>
            </div>
            <div class="min-h-screen flex items-center">
                <div class="w-full">
                    <h2 class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        <span class="en">Body Organization</span>
                        <span class="ar">تنظيم الجسم</span>
                    </h2>
                    <div class="bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-xl border border-white/20 mb-8">
                        <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center en">Levels of Organization</h3>
                        <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center ar">مستويات التنظيم</h3>
                        <div class="flex flex-col items-center">
                            <div class="w-full max-w-3xl mb-6">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-700 en">Simplest</span>
                                    <span class="text-sm font-medium text-gray-700 ar">الأبسط</span>
                                    <span class="text-sm font-medium text-gray-700 en">Most Complex</span>
                                    <span class="text-sm font-medium text-gray-700 ar">الأكثر تعقيدًا</span>
                                </div>
                                <div class="bg-gray-200 rounded-full h-4 w-full">
                                    <div class="bg-gradient-to-r from-blue-400 to-purple-600 rounded-full h-4" style="width: 100%"></div>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 md:grid-cols-6 gap-4 w-full">
                                <div class="interactive-card bg-white p-4 rounded-lg shadow border border-gray-200 transform transition-all hover:scale-105">
                                    <div class="text-blue-600 text-2xl mb-2 text-center"><i class="fas fa-atom"></i></div>
                                    <h4 class="font-semibold mb-1 text-center en">Chemical Level</h4>
                                    <h4 class="font-semibold mb-1 text-center ar">المستوى الكيميائي</h4>
                                    <p class="text-xs text-gray-600 text-center en">Atoms → Molecules</p>
                                    <p class="text-xs text-gray-600 text-center ar">ذرات → جزيئات</p>
                                </div>
                                <div class="interactive-card bg-white p-4 rounded-lg shadow border border-gray-200 transform transition-all hover:scale-105">
                                    <div class="text-blue-600 text-2xl mb-2 text-center"><i class="fas fa-circle"></i></div>
                                    <h4 class="font-semibold mb-1 text-center en">Cellular Level</h4>
                                    <h4 class="font-semibold mb-1 text-center ar">المستوى الخلوي</h4>
                                    <p class="text-xs text-gray-600 text-center en">Basic unit of life</p>
                                    <p class="text-xs text-gray-600 text-center ar">الوحدة الأساسية للحياة</p>
                                </div>
                                <div class="interactive-card bg-white p-4 rounded-lg shadow border border-gray-200 transform transition-all hover:scale-105">
                                    <div class="text-blue-600 text-2xl mb-2 text-center"><i class="fas fa-layer-group"></i></div>
                                    <h4 class="font-semibold mb-1 text-center en">Tissue Level</h4>
                                    <h4 class="font-semibold mb-1 text-center ar">مستوى الأنسجة</h4>
                                    <p class="text-xs text-gray-600 text-center en">Groups of similar cells</p>
                                    <p class="text-xs text-gray-600 text-center ar">مجموعات من الخلايا المتشابهة</p>
                                </div>
                                <div class="interactive-card bg-white p-4 rounded-lg shadow border border-gray-200 transform transition-all hover:scale-105">
                                    <div class="text-blue-600 text-2xl mb-2 text-center"><i class="fas fa-heart"></i></div>
                                    <h4 class="font-semibold mb-1 text-center en">Organ Level</h4>
                                    <h4 class="font-semibold mb-1 text-center ar">مستوى الأعضاء</h4>
                                    <p class="text-xs text-gray-600 text-center en">Multiple tissue types</p>
                                    <p class="text-xs text-gray-600 text-center ar">أنواع متعددة من الأنسجة</p>
                                </div>
                                <div class="interactive-card bg-white p-4 rounded-lg shadow border border-gray-200 transform transition-all hover:scale-105">
                                    <div class="text-blue-600 text-2xl mb-2 text-center"><i class="fas fa-sitemap"></i></div>
                                    <h4 class="font-semibold mb-1 text-center en">System Level</h4>
                                    <h4 class="font-semibold mb-1 text-center ar">مستوى النظام</h4>
                                    <p class="text-xs text-gray-600 text-center en">Related organs</p>
                                    <p class="text-xs text-gray-600 text-center ar">أعضاء مترابطة</p>
                                </div>
                                <div class="interactive-card bg-white p-4 rounded-lg shadow border border-gray-200 transform transition-all hover:scale-105">
                                    <div class="text-blue-600 text-2xl mb-2 text-center"><i class="fas fa-user"></i></div>
                                    <h4 class="font-semibold mb-1 text-center en">Organism Level</h4>
                                    <h4 class="font-semibold mb-1 text-center ar">مستوى الكائن الحي</h4>
                                    <p class="text-xs text-gray-600 text-center en">Complete individual</p>
                                    <p class="text-xs text-gray-600 text-center ar">الفرد الكامل</p>
                </div>
            </div>
        </div>

        <!-- Slide 5.1: Diagrams and Interactives -->
        <div class="slide">
            <div class="min-h-screen flex items-center">
                <div class="w-full">
                    <h2 class="text-5xl font-bold text-center mb-8 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        <span class="en">Diagrams & Interactive Tools</span>
                        <span class="ar">مخططات وأدوات تفاعلية</span>
                    </h2>

                    <!-- Mermaid block: Levels of organization -->
                    <div class="diagram-block shadow-lg mb-6">
                        <div class="diagram-toolbar">
                            <button class="toolbar-btn" data-action="copy" data-target="#orgLevelsCode"><i class="fas fa-copy mr-1"></i>Copy</button>
                            <button class="toolbar-btn" data-action="theme"><i class="fas fa-adjust mr-1"></i>Theme</button>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2 en">Diagram: Levels of Organization</h3>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2 ar">مخطط: مستويات التنظيم</h3>
<pre id="orgLevelsCode" class="hidden">flowchart LR
A[Atoms] --> B[Molecules]
B --> C[Organelles]
C --> D[Cells]
D --> E[Tissues]
E --> F[Organs]
F --> G[Systems]
G --> H[Organism]</pre>
                        <div class="mermaid">flowchart LR
A[Atoms] --> B[Molecules]
B --> C[Organelles]
C --> D[Cells]
D --> E[Tissues]
E --> F[Organs]
F --> G[Systems]
G --> H[Organism]</div>
                    </div>

                    <!-- Mermaid block: Reflex arc circuit -->
                    <div class="diagram-block shadow-lg mb-6">
                        <div class="diagram-toolbar">
                            <button class="toolbar-btn" data-action="copy" data-target="#reflexCode"><i class="fas fa-copy mr-1"></i>Copy</button>
                            <button class="toolbar-btn" data-action="theme"><i class="fas fa-adjust mr-1"></i>Theme</button>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2 en">Circuit Diagram: Reflex Arc</h3>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2 ar">دائرة: القوس الانعكاسي</h3>
<pre id="reflexCode" class="hidden">flowchart LR
Stimulus((Stimulus))
Stimulus -->|AP| Receptor[[Receptor]]
Receptor --> Afferent((Sensory Neuron))
Afferent --> CNS{{Spinal Cord Interneuron}}
CNS --> Efferent((Motor Neuron))
Efferent --> Effector[[Muscle]]
Effector --> Response((Response))</pre>
                        <div class="mermaid">flowchart LR
Stimulus((Stimulus))
Stimulus -->|AP| Receptor[[Receptor]]
Receptor --> Afferent((Sensory Neuron))
Afferent --> CNS{{Spinal Cord Interneuron}}
CNS --> Efferent((Motor Neuron))
Efferent --> Effector[[Muscle]]
Effector --> Response((Response))</div>
                    </div>

                    <!-- Hotspot image -->
                    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 mb-6">
                        <h3 class="text-xl font-semibold text-gray-800 mb-4 en">Interactive Hotspots</h3>
                        <h3 class="text-xl font-semibold text-gray-800 mb-4 ar">نقاط تفاعلية</h3>
                        <div class="hotspot-wrapper max-w-xl mx-auto relative">
                            <img src="https://images.unsplash.com/photo-1586773860418-d37222d8fce3?q=80&w=800&auto=format&fit=crop" alt="Torso Illustration" class="rounded-xl shadow">
                            <button class="hotspot" style="top: 25%; left: 45%;" aria-label="Heart hotspot" tabindex="0"></button><div class="hotspot-tooltip">Heart: pumps blood</div>
                            <button class="hotspot" style="top: 15%; left: 48%;" aria-label="Lungs hotspot" tabindex="0"></button><div class="hotspot-tooltip">Lungs: gas exchange</div>
                            <button class="hotspot" style="top: 60%; left: 50%;" aria-label="Stomach hotspot" tabindex="0"></button><div class="hotspot-tooltip">Stomach: digestion</div>
                        </div>
                    </div>

                    <!-- Tabs and Quiz -->
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20 tabs">
                            <div role="tablist" aria-label="Anatomy tabs" class="flex gap-2 mb-4">
                                <button role="tab" aria-selected="true" aria-controls="tab1" id="tab-btn-1" class="px-3 py-2 rounded-lg bg-indigo-100">Histology</button>
                                <button role="tab" aria-selected="false" aria-controls="tab2" id="tab-btn-2" class="px-3 py-2 rounded-lg">Osteology</button>
                                <button role="tab" aria-selected="false" aria-controls="tab3" id="tab-btn-3" class="px-3 py-2 rounded-lg">Myology</button>
                            </div>
                            <div id="tab1" role="tabpanel" aria-labelledby="tab-btn-1">
                                <p class="text-gray-700">Microscopic study of tissues and cells.</p>
                            </div>
                            <div id="tab2" class="hidden" role="tabpanel" aria-labelledby="tab-btn-2">
                                <p class="text-gray-700">Study of bones and skeletal elements.</p>
                            </div>
                            <div id="tab3" class="hidden" role="tabpanel" aria-labelledby="tab-btn-3">
                                <p class="text-gray-700">Study of muscles and their functions.</p>
                            </div>
                        </div>

                        <div class="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                            <div class="collapsible">
                                <button class="w-full flex items-center justify-between px-4 py-2 rounded-lg bg-blue-50">
                                    <span class="font-semibold text-blue-800">Quick Quiz: Directional Terms</span>
                                    <i class="fas fa-chevron-down text-blue-800"></i>
                                </button>
                                <div class="collapsible-content mt-4">
                                    <div class="space-y-3">
                                        <div class="quiz-option px-4 py-2 rounded-lg border" data-correct="false">The nose is posterior to the ears.</div>
                                        <div class="quiz-option px-4 py-2 rounded-lg border" data-correct="true">The sternum is anterior to the heart.</div>
                                        <div class="quiz-option px-4 py-2 rounded-lg border" data-correct="false">The ankle is proximal to the knee.</div>
                                    </div>
                                </div>
                            </div>
                            <p id="quizFeedback" class="mt-3 text-sm"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    </div>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                            <h3 class="text-xl font-semibold text-gray-800 mb-4 en">Body Cavities</h3>
                            <h3 class="text-xl font-semibold text-gray-800 mb-4 ar">تجاويف الجسم</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-blue-700 mb-2 en">Dorsal Cavity</h4>
                                    <h4 class="font-medium text-blue-700 mb-2 ar">التجويف الظهري</h4>
                                    <ul class="space-y-1 text-sm text-gray-700 en">
                                        <li><i class="fas fa-brain mr-2 text-blue-500"></i> Cranial (brain)</li>
                                        <li><i class="fas fa-spine mr-2 text-blue-500"></i> Spinal (spinal cord)</li>
                                    </ul>
                                    <ul class="space-y-1 text-sm text-gray-700 ar">
                                        <li><i class="fas fa-brain mr-2 text-blue-500"></i> القحفي (الدماغ)</li>
                                        <li><i class="fas fa-spine mr-2 text-blue-500"></i> الشوكي (الحبل الشوكي)</li>
                                    </ul>
                                </div>
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-blue-700 mb-2 en">Ventral Cavity</h4>
                                    <h4 class="font-medium text-blue-700 mb-2 ar">التجويف البطني</h4>
                                    <ul class="space-y-1 text-sm text-gray-700 en">
                                        <li><i class="fas fa-lungs mr-2 text-blue-500"></i> Thoracic (heart, lungs)</li>
                                        <li><i class="fas fa-stomach mr-2 text-blue-500"></i> Abdominal (digestive)</li>
                                        <li><i class="fas fa-venus-mars mr-2 text-blue-500"></i> Pelvic (reproductive)</li>
                                    </ul>
                                    <ul class="space-y-1 text-sm text-gray-700 ar">
                                        <li><i class="fas fa-lungs mr-2 text-blue-500"></i> الصدري (القلب، الرئتين)</li>
                                        <li><i class="fas fa-stomach mr-2 text-blue-500"></i> البطني (الجهاز الهضمي)</li>
                                        <li><i class="fas fa-venus-mars mr-2 text-blue-500"></i> الحوضي (الجهاز التناسلي)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                            <h3 class="text-xl font-semibold text-gray-800 mb-4 en">Body Regions</h3>
                            <h3 class="text-xl font-semibold text-gray-800 mb-4 ar">مناطق الجسم</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-purple-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-purple-700 mb-2 en">Axial Region</h4>
                                    <h4 class="font-medium text-purple-700 mb-2 ar">المنطقة المحورية</h4>
                                    <ul class="space-y-1 text-sm text-gray-700 en">
                                        <li><i class="fas fa-head-side mr-2 text-purple-500"></i> Head (cephalic)</li>
                                        <li><i class="fas fa-user mr-2 text-purple-500"></i> Neck (cervical)</li>
                                        <li><i class="fas fa-lungs mr-2 text-purple-500"></i> Chest (thoracic)</li>
                                        <li><i class="fas fa-stomach mr-2 text-purple-500"></i> Abdomen</li>
                                    </ul>
                                    <ul class="space-y-1 text-sm text-gray-700 ar">
                                        <li><i class="fas fa-head-side mr-2 text-purple-500"></i> الرأس (القحفي)</li>
                                        <li><i class="fas fa-user mr-2 text-purple-500"></i> الرقبة (العنقي)</li>
                                        <li><i class="fas fa-lungs mr-2 text-purple-500"></i> الصدر (الصدري)</li>
                                        <li><i class="fas fa-stomach mr-2 text-purple-500"></i> البطن</li>
                                    </ul>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-purple-700 mb-2 en">Appendicular Region</h4>
                                    <h4 class="font-medium text-purple-700 mb-2 ar">المنطقة الطرفية</h4>
                                    <ul class="space-y-1 text-sm text-gray-700 en">
                                        <li><i class="fas fa-hand mr-2 text-purple-500"></i> Upper limbs (arms)</li>
                                        <li><i class="fas fa-shoe-prints mr-2 text-purple-500"></i> Lower limbs (legs)</li>
                                        <li><i class="fas fa-circle mr-2 text-purple-500"></i> Shoulder (pectoral)</li>
                                        <li><i class="fas fa-circle mr-2 text-purple-500"></i> Hip (pelvic)</li>
                                    </ul>
                                    <ul class="space-y-1 text-sm text-gray-700 ar">
                                        <li><i class="fas fa-hand mr-2 text-purple-500"></i> الأطراف العلوية (الذراعين)</li>
                                        <li><i class="fas fa-shoe-prints mr-2 text-purple-500"></i> الأطراف السفلية (الساقين)</li>
                                        <li><i class="fas fa-circle mr-2 text-purple-500"></i> الكتف (الصدري)</li>
                                        <li><i class="fas fa-circle mr-2 text-purple-500"></i> الورك (الحوضي)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 6: Skeletal System Overview -->
        <div class="slide">
            <div class="min-h-screen flex items-center">
                <div class="w-full">
                    <h2 class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        <span class="en">Skeletal System</span>
                        <span class="ar">الجهاز الهيكلي</span>
                    </h2>
                    <div class="grid md:grid-cols-2 gap-8 mb-8">
                        <div class="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-bone text-4xl text-blue-600 mr-4 system-icon"></i>
                                <h3 class="text-2xl font-semibold text-gray-800 en">Functions</h3>
                                <h3 class="text-2xl font-semibold text-gray-800 ar">الوظائف</h3>
                            </div>
                            <ul class="space-y-3 text-gray-700 en">
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>Support: Provides framework for the body</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>Protection: Shields vital organs</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>Movement: Works with muscles for locomotion</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>Blood cell production: In red bone marrow</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>Mineral storage: Calcium and phosphorus</span>
                                </li>
                            </ul>
                            <ul class="space-y-3 text-gray-700 ar">
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>الدعم: يوفر إطارًا للجسم</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>الحماية: يحمي الأعضاء الحيوية</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>الحركة: يعمل مع العضلات للتنقل</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>إنتاج خلايا الدم: في نخاع العظام الأحمر</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <span>تخزين المعادن: الكالسيوم والفوسفور</span>
                                </li>
                            </ul>
                        </div>
                        <div class="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-x-ray text-4xl text-purple-600 mr-4 system-icon"></i>
                                <h3 class="text-2xl font-semibold text-gray-800 en">Composition</h3>
                                <h3 class="text-2xl font-semibold text-gray-800 ar">التكوين</h3>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-purple-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-purple-800 mb-2 en">Axial Skeleton (80 bones)</h4>
                                    <h4 class="font-medium text-purple-800 mb-2 ar">الهيكل المحوري (80 عظمة)</h4>
                                    <ul class="space-y-1 text-sm text-gray-700 en">
                                        <li><i class="fas fa-skull mr-2 text-purple-500"></i> Skull (22)</li>
                                        <li><i class="fas fa-grip-lines mr-2 text-purple-500"></i> Hyoid (1)</li>
                                        <li><i class="fas fa-ear mr-2 text-purple-500"></i> Ear ossicles (6)</li>
                                        <li><i class="fas fa-spine mr-2 text-purple-500"></i> Vertebrae (26)</li>
                                        <li><i class="fas fa-grip-lines mr-2 text-purple-500"></i> Ribs & sternum (25)</li>
                                    </ul>
                                    <ul class="space-y-1 text-sm text-gray-700 ar">
                                        <li><i class="fas fa-skull mr-2 text-purple-500"></i> الجمجمة (22)</li>
                                        <li><i class="fas fa-grip-lines mr-2 text-purple-500"></i> العظم اللامي (1)</li>
                                        <li><i class="fas fa-ear mr-2 text-purple-500"></i> عظيمات الأذن (6)</li>
                                        <li><i class="fas fa-spine mr-2 text-purple-500"></i> الفقرات (26)</li>
                                        <li><i class="fas fa-grip-lines mr-2 text-purple-500"></i> الأضلاع والقص (25)</li>
                                    </ul>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-purple-800 mb-2 en">Appendicular Skeleton (126 bones)</h4>
                                    <h4 class="font-medium text-purple-800 mb-2 ar">الهيكل الطرفي (126 عظمة)</h4>
                                    <ul class="space-y-1 text-sm text-gray-700 en">
                                        <li><i class="fas fa-circle mr-2 text-purple-500"></i> Pectoral girdles (4)</li>
                                        <li><i class="fas fa-hand mr-2 text-purple-500"></i> Upper limbs (60)</li>
                                        <li><i class="fas fa-circle mr-2 text-purple-500"></i> Pelvic girdle (2)</li>
                                        <li><i class="fas fa-shoe-prints mr-2 text-purple-500"></i> Lower limbs (60)</li>
                                    </ul>
                                    <ul class="space-y-1 text-sm text-gray-700 ar">
                                        <li><i class="fas fa-circle mr-2 text-purple-500"></i> الحزام الكتفي (4)</li>
                                        <li><i class="fas fa-hand mr-2 text-purple-500"></i> الأطراف العلوية (60)</li>
                                        <li><i class="fas fa-circle mr-2 text-purple-500"></i> الحزام الحوضي (2)</li>
                                        <li><i class="fas fa-shoe-prints mr-2 text-purple-500"></i> الأطراف السفلية (60)</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="mt-4 bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-medium text-blue-800 mb-2 en">Total: 206 bones in adult human skeleton</h4>
                                <h4 class="font-medium text-blue-800 mb-2 ar">المجموع: 206 عظام في الهيكل العظمي البشري البالغ</h4>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-layer-group text-4xl text-green-600 mr-4 system-icon"></i>
                            <h3 class="text-2xl font-semibold text-gray-800 en">Bone Structure</h3>
                            <h3 class="text-2xl font-semibold text-gray-800 ar">بنية العظام</h3>
                        </div>
                        <div class="grid md:grid-cols-3 gap-4">
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-medium text-green-800 mb-2 en">Compact Bone</h4>
                                <h4 class="font-medium text-green-800 mb-2 ar">العظم المضغوط</h4>
                                <p class="text-sm text-gray-700 en">Dense, solid outer layer with haversian systems</p>
                                <p class="text-sm text-gray-700 ar">طبقة خارجية صلبة وكثيفة مع أنظمة هافرسية</p>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-medium text-green-800 mb-2 en">Spongy Bone</h4>
                                <h4 class="font-medium text-green-800 mb-2 ar">العظم الإسفنجي</h4>
                                <p class="text-sm text-gray-700 en">Porous inner layer with trabeculae and red marrow</p>
                                <p class="text-sm text-gray-700 ar">طبقة داخلية مسامية مع الحواجز العظمية والنخاع الأحمر</p>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-medium text-green-800 mb-2 en">Bone Marrow</h4>
                                <h4 class="font-medium text-green-800 mb-2 ar">نخاع العظم</h4>
                                <p class="text-sm text-gray-700 en">Red marrow (hematopoietic) and yellow marrow (fat storage)</p>
                                <p class="text-sm text-gray-700 ar">النخاع الأحمر (مكون للدم) والنخاع الأصفر (تخزين الدهون)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 7: Joints & Articulations -->
        <div class="slide">
            <div class="min-h-screen flex items-center">
                <div class="w-full">
                    <h2 class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        <span class="en">Joints & Articulations</span>
                        <span class="ar">المفاصل والمفصليات</span>
                    </h2>
                    <div class="grid md:grid-cols-3 gap-8 mb-8">
                        <div class="interactive-card bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                            <div class="text-center mb-6">
                                <i class="fas fa-link text-5xl text-blue-600 system-icon"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-800 mb-4 text-center en">Fibrous Joints</h3>
                            <h3 class="text-xl font-bold text-gray-800 mb-4 text-center ar">المفاصل الليفية</h3>
                            <p class="text-gray-700 mb-4 en">
                                Bones connected by fibrous tissue with little to no movement.
                            </p>
                            <p class="text-gray-700 mb-4 ar">
                                عظام متصلة بأنسجة ليفية مع حركة قليلة أو معدومة.
                            </p>
                            <div class="bg-blue-50 p-3 rounded-lg">
                                <h4 class="font-medium text-blue-800 mb-2 en">Examples:</h4>
                                <h4 class="font-medium text-blue-800 mb-2 ar">أمثلة:</h4>
                                <ul class="text-sm text-gray-700 en">
                                    <li><i class="fas fa-arrow-right mr-2 text-blue-500"></i> Sutures of skull</li>
                                    <li><i class="fas fa-arrow-right mr-2 text-blue-500"></i> Gomphoses (teeth in sockets)</li>
                                </ul>
                                <ul class="text-sm text-gray-700 ar">
                                    <li><i class="fas fa-arrow-right mr-2 text-blue-500"></i> دروز الجمجمة</li>
                                    <li><i class="fas fa-arrow-right mr-2 text-blue-500"></i> المفاصل الوتدية (الأسنان في تجاويفها)</li>
                                </ul>
                            </div>
                        </div>
                        <div class="interactive-card bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                            <div class="text-center mb-6">
                                <i class="fas fa-hockey-puck text-5xl text-purple-600 system-icon"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-800 mb-4 text-center en">Cartilaginous Joints</h3>
                            <h3 class="text-xl font-bold text-gray-800 mb-4 text-center ar">المفاصل الغضروفية</h3>
                            <p class="text-gray-700 mb-4 en">
                                Bones connected by cartilage with limited movement.
                            </p>
                            <p class="text-gray-700 mb-4 ar">
                                عظام متصلة بغضروف مع حركة محدودة.
                            </p>
                            <div class="bg-purple-50 p-3 rounded-lg">
                                <h4 class="font-medium text-purple-800 mb-2 en">Examples:</h4>
                                <h4 class="font-medium text-purple-800 mb-2 ar">أمثلة:</h4>
                                <ul class="text-sm text-gray-700 en">
                                    <li><i class="fas fa-arrow-right mr-2 text-purple-500"></i> Symphysis pubis</li>
                                    <li><i class="fas fa-arrow-right mr-2 text-purple-500"></i> Intervertebral discs</li>
                                </ul>
                                <ul class="text-sm text-gray-700 ar">
                                    <li><i class="fas fa-arrow-right mr-2 text-purple-500"></i> الارتفاق العاني</li>
                                    <li><i class="fas fa-arrow-right mr-2 text-purple-500"></i> الأقراص بين الفقرات</li>
                                </ul>
                            </div>
                        </div>
                        <div class="interactive-card bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                            <div class="text-center mb-6">
                                <i class="fas fa-walking text-5xl text-green-600 system-icon"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-800 mb-4 text-center en">Synovial Joints</h3>
                            <h3 class="text-xl font-bold text-gray-800 mb-4 text-center ar">المفاصل الزلالية</h3>
                            <p class="text-gray-700 mb-4 en">
                                Most movable joints with synovial fluid in a joint cavity.
                            </p>
                            <p class="text-gray-700 mb-4 ar">
                                المفاصل الأكثر حركة مع سائل زلالي في تجويف المفصل.
                            </p>
                            <div class="bg-green-50 p-3 rounded-lg">
                                <h4 class="font-medium text-green-800 mb-2 en">Examples:</h4>
                                <h4 class="font-medium text-green-800 mb-2 ar">أمثلة:</h4>
                                <ul class="text-sm text-gray-700 en">
                                    <li><i class="fas fa-arrow-right mr-2 text-green-500"></i> Shoulder, hip, knee</li>
                                    <li><i class="fas fa-arrow-right mr-2 text-green-500"></i> Elbow, wrist, ankle</li>
                                </ul>
                                <ul class="text-sm text-gray-700 ar">
                                    <li><i class="fas fa-arrow-right mr-2 text-green-500"></i> الكتف، الورك، الركبة</li>
                                    <li><i class="fas fa-arrow-right mr-2 text-green-500"></i> المرفق، الرسغ، الكاحل</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                        <h3 class="text-2xl font-semibold text-gray-800 mb-6 text-center en">Types of Synovial Joints</h3>
                        <h3 class="text-2xl font-semibold text-gray-800 mb-6 text-center ar">أنواع المفاصل الزلالية</h3>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-medium text-blue-800 mb-2 en">Ball and Socket</h4>
                                <h4 class="font-medium text-blue-800 mb-2 ar">الكرة والتجويف</h4>
                                <p class="text-sm text-gray-700 en">Multiaxial movement (shoulder, hip)</p>
                                <p class="text-sm text-gray-700 ar">حركة متعددة المحاور (الكتف، الورك)</p>
                            </div>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-medium text-blue-800 mb-2 en">Hinge</h4>
                                <h4 class="font-medium text-blue-800 mb-2 ar">المفصل المفصلي</h4>
                                <p class="text-sm text-gray-700 en">Uniaxial movement (elbow, knee)</p>
                                <p class="text-sm text-gray-700 ar">حركة أحادية المحور (المرفق، الركبة)</p>
                            </div>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-medium text-blue-800 mb-2 en">Pivot</h4>
                                <h4 class="font-medium text-blue-800 mb-2 ar">المفصل المحوري</h4>
                                <p class="text-sm text-gray-700 en">Rotation (atlas-axis joint)</p>
                                <p class="text-sm text-gray-700 ar">الدوران (مفصل الفهقة-المحور)</p>
                            </div>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-medium text-blue-800 mb-2 en">Gliding</h4>
                                <h4 class="font-medium text-blue-800 mb-2 ar">المفصل الانزلاقي</h4>
                                <p class="text-sm text-gray-700 en">Sliding movement (wrist, ankle)</p>
                                <p class="text-sm text-gray-700 ar">حركة انزلاقية (الرسغ، الكاحل)</p>
                            </div>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-medium text-blue-800 mb-2 en">Saddle</h4>
                                <h4 class="font-medium text-blue-800 mb-2 ar">المفصل السرجي</h4>
                                <p class="text-sm text-gray-700 en">Biaxial movement (thumb carpometacarpal)</p>
                                <p class="text-sm text-gray-700 ar">حركة ثنائية المحور (مفصل الإبهام الرسغي السنعي)</p>
                            </div>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-medium text-blue-800 mb-2 en">Condyloid</h4>
                                <h4 class="font-medium text-blue-800 mb-2 ar">المفصل اللقمي</h4>
                                <p class="text-sm text-gray-700 en">Biaxial movement (wrist)</p>
                                <p class="text-sm text-gray-700 ar">حركة ثنائية المحور (الرسغ)</p>
('DOMContentLoaded', function() {
            const slides = document.querySelectorAll('.slide');
            const totalSlides = slides.length;
            const totalSlidesElement = document.getElementById('totalSlides');
            const currentSlideElement = document.getElementById('currentSlide');
            const progressBar = document.getElementById('progressBar');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            
            let currentSlideIndex = 0;
            
            // Update total slides count
            totalSlidesElement.textContent = totalSlides;
            
            // Function to show a specific slide
            function showSlide(index) {
                // Hide all slides
                slides.forEach(slide => {
                    slide.classList.remove('active');
                });
                
                // Show the current slide
                slides[index].classList.add('active');
                
                // Update current slide number
                currentSlideElement.textContent = index + 1;
                
                // Update progress bar
                const progressPercentage = ((index + 1) / totalSlides) * 100;
                progressBar.style.width = `${progressPercentage}%`;
            }
            
            // Event listeners for navigation buttons
            prevBtn.addEventListener('click', function() {
                if (currentSlideIndex > 0) {
                    currentSlideIndex--;
                    showSlide(currentSlideIndex);
                }
            });
            
            nextBtn.addEventListener('click', function() {
                if (currentSlideIndex < totalSlides - 1) {
                    currentSlideIndex++;
                    showSlide(currentSlideIndex);
                }
            });
            
            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'ArrowRight' || e.key === ' ') {
                    if (currentSlideIndex < totalSlides - 1) {
                        currentSlideIndex++;
                        showSlide(currentSlideIndex);
                    }
                } else if (e.key === 'ArrowLeft') {
                    if (currentSlideIndex > 0) {
                        currentSlideIndex--;
                        showSlide(currentSlideIndex);
                    }
                }
            });
            
            // Initialize
            showSlide(currentSlideIndex);
        });
        // Enhancements JS
        document.addEventListener('DOMContentLoaded', () => {
            // Mermaid init
            if (window.mermaid) {
                mermaid.initialize({ startOnLoad: true, theme: 'default' });
            }

            // Help modal
            const helpModal = document.getElementById('helpModal');
            const openHelp = document.getElementById('openHelp');
            const closeHelp = document.getElementById('closeHelp');
            if (openHelp && helpModal) {
                openHelp.addEventListener('click', () => helpModal.classList.remove('hidden'));
            }
            if (closeHelp && helpModal) {
                closeHelp.addEventListener('click', () => helpModal.classList.add('hidden'));
            }
            if (helpModal) {
                helpModal.addEventListener('click', (e) => {
                    if (e.target === helpModal) helpModal.classList.add('hidden');
                });
            }

            // Language toggle
            const toggleLang = document.getElementById('toggleLang');
            if (toggleLang) {
                toggleLang.addEventListener('click', () => {
                    document.querySelectorAll('.en').forEach(el => el.classList.toggle('hidden'));
                    document.querySelectorAll('.ar').forEach(el => el.classList.toggle('hidden'));
                });
            }

            // Tabs
            const tabsRoot = document.querySelector('.tabs');
            if (tabsRoot) {
                const tabs = tabsRoot.querySelectorAll('[role="tab"]');
                const panels = ['tab1','tab2','tab3'].map(id => document.getElementById(id));
                tabs.forEach((tab, idx) => {
                    tab.addEventListener('click', () => {
                        tabs.forEach(t => t.setAttribute('aria-selected','false'));
                        panels.forEach(p => p.classList.add('hidden'));
                        tab.setAttribute('aria-selected','true');
                        panels[idx].classList.remove('hidden');
                    });
                });
            }

            // Collapsible + quiz
            document.querySelectorAll('.collapsible').forEach(col => {
                const btn = col.querySelector('button');
                const content = col.querySelector('.collapsible-content');
                btn.addEventListener('click', () => {
                    col.classList.toggle('open');
                });
                col.querySelectorAll('.quiz-option').forEach(opt => {
                    opt.addEventListener('click', () => {
                        col.querySelectorAll('.quiz-option').forEach(o => o.classList.remove('correct','incorrect'));
                        const ok = opt.getAttribute('data-correct') === 'true';
                        opt.classList.add(ok ? 'correct' : 'incorrect');
                        const fb = document.getElementById('quizFeedback');
                        if (fb) fb.textContent = ok ? 'Correct!' : 'Try again.';
                    });
                });
            });

            // Diagram toolbar actions
            document.querySelectorAll('.diagram-toolbar .toolbar-btn').forEach(btn => {
                btn.addEventListener('click', async () => {
                    const action = btn.getAttribute('data-action');
                    if (action === 'copy') {
                        const sel = btn.getAttribute('data-target');
                        const pre = document.querySelector(sel);
                        if (pre) {
                            await navigator.clipboard.writeText(pre.textContent.trim());
                            btn.textContent = 'Copied';
                            setTimeout(() => btn.innerHTML = '<i class="fas fa-copy mr-1"></i>Copy', 1200);
                        }
                    }
                    if (action === 'theme' && window.mermaid) {
                        const current = mermaid.mermaidAPI.getConfig().theme || 'default';
                        const next = current === 'default' ? 'forest' : current === 'forest' ? 'neutral' : 'default';
                        mermaid.initialize({ startOnLoad: true, theme: next });
                        // Re-render by replacing innerHTML to trigger mermaid
                        document.querySelectorAll('.mermaid').forEach(el => {
                            el.innerHTML = el.innerText;
                        });
                        mermaid.init();
                    }
                });
            });
        });
    </script>
</body>
</html>
