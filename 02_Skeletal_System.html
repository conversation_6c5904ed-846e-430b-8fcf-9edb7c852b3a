<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skeletal System: Bones & Joints | UST BME</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }
        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .fade-in { animation: fadeIn 0.8s ease-out; }
        .slide-in-left { animation: slideInLeft 0.8s ease-out; }
        .slide-in-right { animation: slideInRight 0.8s ease-out; }
        .pulse-animation { animation: pulse 2s infinite; }
        .rotate-animation { animation: rotate 2s linear infinite; }
        
        .slide {
            display: none;
            min-height: 100vh;
        }
        .slide.active {
            display: block;
        }
        
        .interactive-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .interactive-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .bone-structure {
            position: relative;
            width: 300px;
            height: 400px;
            margin: 0 auto;
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid #d1d5db;
        }
        
        .bone-layers {
            width: 200px;
            height: 300px;
            position: relative;
            border-radius: 10px;
        }
        
        .cortical-bone {
            position: absolute;
            width: 100%;
            height: 100%;
            background: #6b7280;
            border-radius: 10px;
        }
        
        .spongy-bone {
            position: absolute;
            width: 60%;
            height: 60%;
            background: #9ca3af;
            border-radius: 5px;
            top: 20%;
            left: 20%;
        }
        
        .marrow-cavity {
            position: absolute;
            width: 30%;
            height: 30%;
            background: #fbbf24;
            border-radius: 50%;
            top: 35%;
            left: 35%;
        }
        
        .quiz-option {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .quiz-option:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .quiz-option.correct {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
        }
        
        .quiz-option.incorrect {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 font-sans">
    <!-- Navigation Controls -->
    <div class="fixed bottom-6 right-6 z-50 flex space-x-3">
        <button id="prevBtn" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110">
            <i class="fas fa-arrow-left text-lg"></i>
        </button>
        <button id="nextBtn" class="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110">
            <i class="fas fa-arrow-right text-lg"></i>
        </button>
    </div>

    <!-- Slide Counter -->
    <div class="fixed bottom-6 left-6 z-50 bg-white/90 backdrop-blur-sm px-6 py-3 rounded-full shadow-lg border border-white/20">
        <span class="text-lg font-semibold text-gray-700">
            <span id="currentSlide">1</span> / <span id="totalSlides">20</span>
        </span>
    </div>

    <!-- Progress Bar -->
    <div class="fixed top-0 left-0 w-full h-1 bg-gray-200 z-50">
        <div id="progressBar" class="h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-300" style="width: 5%"></div>
    </div>

    <!-- Slides Container -->
    <div class="container mx-auto px-6 py-8 max-w-7xl">
        
        <!-- Slide 1: Title Slide -->
        <div class="slide active fade-in">
            <div class="min-h-screen flex items-center justify-center">
                <div class="text-center">
                    <div class="mb-8">
                        <i class="fas fa-bone text-8xl text-gray-600 pulse-animation"></i>
                    </div>
                    <h1 class="text-6xl font-bold bg-gradient-to-r from-gray-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent mb-6">
                        Skeletal System
                    </h1>
                    <h2 class="text-3xl text-gray-700 mb-8">Bones & Joints</h2>
                    <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/20 max-w-2xl mx-auto">
                        <p class="text-xl text-gray-600 mb-4">University of Science and Technology</p>
                        <p class="text-lg text-blue-600 font-semibold mb-2">College of Engineering - Biomedical Engineering</p>
                        <p class="text-gray-500 mb-4">Dr. Mohammed Yagoub Esmail | BME 2025</p>
                        <p class="text-sm text-gray-500">📧 <EMAIL></p>
                        <p class="text-sm text-gray-500">📱 +249912867327 | +966538076790</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2: Learning Objectives -->
        <div class="slide">
            <div class="min-h-screen flex items-center">
                <div class="w-full">
                    <h2 class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-gray-600 to-blue-600 bg-clip-text text-transparent">
                        Learning Objectives
                    </h2>
                    <div class="grid md:grid-cols-2 gap-8">
                        <div class="space-y-6">
                            <div class="interactive-card bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                                <div class="flex items-center mb-4">
                                    <i class="fas fa-bullseye text-3xl text-blue-600 mr-4"></i>
                                    <h3 class="text-xl font-semibold text-gray-800">Primary Goals</h3>
                                </div>
                                <ul class="space-y-3 text-gray-700">
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                        <span>Understand bone structure and composition</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                        <span>Classify different types of bones and joints</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                        <span>Explain bone development and remodeling</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="space-y-6">
                            <div class="interactive-card bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/20">
                                <div class="flex items-center mb-4">
                                    <i class="fas fa-graduation-cap text-3xl text-purple-600 mr-4"></i>
                                    <h3 class="text-xl font-semibold text-gray-800">Learning Outcomes</h3>
                                </div>
                                <ul class="space-y-3 text-gray-700">
                                    <li class="flex items-start">
                                        <i class="fas fa-star text-yellow-500 mt-1 mr-3"></i>
                                        <span>Identify major bones and their features</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-star text-yellow-500 mt-1 mr-3"></i>
                                        <span>Analyze joint movements and mechanics</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-star text-yellow-500 mt-1 mr-3"></i>
                                        <span>Apply knowledge to biomedical engineering</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3: Skeletal System Functions -->
        <div class="slide">
            <div class="min-h-screen flex items-center">
                <div class="w-full">
                    <h2 class="text-5xl font-bold text-center mb-12 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                        Functions of the Skeletal System
                    </h2>
                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="interactive-card bg-gradient-to-br from-blue-500 to-blue-600 p-8 rounded-2xl text-white text-center">
                            <i class="fas fa-shield-alt text-5xl mb-4"></i>
                            <h3 class="text-xl font-bold mb-3">Protection</h3>
                            <p class="text-sm opacity-90">Protects vital organs from injury</p>
                            <div class="mt-3 text-xs bg-white/20 p-2 rounded">
                                Example: Skull protects brain
                            </div>
                        </div>
                        <div class="interactive-card bg-gradient-to-br from-green-500 to-green-600 p-8 rounded-2xl text-white text-center">
                            <i class="fas fa-columns text-5xl mb-4"></i>
                            <h3 class="text-xl font-bold mb-3">Support</h3>
                            <p class="text-sm opacity-90">Provides structural framework</p>
                            <div class="mt-3 text-xs bg-white/20 p-2 rounded">
                                Example: Vertebral column supports body
                            </div>
                        </div>
                        <div class="interactive-card bg-gradient-to-br from-purple-500 to-purple-600 p-8 rounded-2xl text-white text-center">
                            <i class="fas fa-running text-5xl mb-4"></i>
                            <h3 class="text-xl font-bold mb-3">Movement</h3>
                            <p class="text-sm opacity-90">Provides attachment for muscles</p>
                            <div class="mt-3 text-xs bg-white/20 p-2 rounded">
                                Example: Lever system for locomotion
                            </div>
                        </div>
                        <div class="interactive-card bg-gradient-to-br from-red-500 to-red-600 p-8 rounded-2xl text-white text-center">
                            <i class="fas fa-tint text-5xl mb-4"></i>
                            <h3 class="text-xl font-bold mb-3">Blood Cell Production</h3>
                            <p class="text-sm opacity-90">Hematopoiesis in bone marrow</p>
                            <div class="mt-3 text-xs bg-white/20 p-2 rounded">
                                Example: Red blood cell formation
                            </div>
                        </div>
                        <div class="interactive-card bg-gradient-to-br from-yellow-500 to-yellow-600 p-8 rounded-2xl text-white text-center">
                            <i class="fas fa-battery-full text-5xl mb-4"></i>
                            <h3 class="text-xl font-bold mb-3">Mineral Storage</h3>
                            <p class="text-sm opacity-90">Stores calcium and phosphorus</p>
                            <div class="mt-3 text-xs bg-white/20 p-2 rounded">
                                Example: Calcium homeostasis
                            </div>
                        </div>
                        <div class="interactive-card bg-gradient-to-br from-indigo-500 to-indigo-600 p-8 rounded-2xl text-white text-center">
                            <i class="fas fa-oil-can text-5xl mb-4"></i>
                            <h3 class="text-xl font-bold mb-3">Fat Storage</h3>
                            <p class="text-sm opacity-90">Yellow marrow stores lipids</p>
                            <div class="mt-3 text-xs bg-white/20 p-2 rounded">
                                Example: Energy reserve
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script>
        // Slide navigation functionality
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        document.getElementById('totalSlides').textContent = totalSlides;

        function showSlide(index) {
            slides.forEach(slide => slide.classList.remove('active'));
            slides[index].classList.add('active');

            document.getElementById('currentSlide').textContent = index + 1;

            // Update progress bar
            const progress = ((index + 1) / totalSlides) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }

        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                currentSlideIndex++;
                showSlide(currentSlideIndex);
            }
        }

        function prevSlide() {
            if (currentSlideIndex > 0) {
                currentSlideIndex--;
                showSlide(currentSlideIndex);
            }
        }

        // Event listeners
        document.getElementById('nextBtn').addEventListener('click', nextSlide);
        document.getElementById('prevBtn').addEventListener('click', prevSlide);

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                prevSlide();
            }
        });

        // Initialize
        showSlide(0);
    </script>
</body>
</html>
