---
description: Repository Information Overview
alwaysApply: true
---

# Human Anatomy & Physiology Course Materials

## Summary
This repository contains educational materials for a Human Anatomy & Physiology course designed for Biomedical Engineering students at the University of Science and Technology. The materials include interactive HTML presentations and comprehensive Word documents covering various aspects of human anatomy and physiology.

## Structure
- **HTML Presentations**: Interactive slide-based presentations with modern UI elements
- **Word Documents**: Comprehensive course content in document format
- **Configuration Directories**: Supporting configuration for development environments

## Content Overview

### Educational Materials
**Course Title**: Human Anatomy & Physiology (BME 2025)
**Instructor**: Dr. <PERSON> Yagoub Esmail
**Institution**: University of Science and Technology - College of Engineering - Biomedical Engineering Department
**Contact**: <EMAIL> | +249912867327, +966538076790

### Main Documents
- **علم التشريح للمهندسين English.docx**: Comprehensive course material in English
- **علم التشريح ووظائف الأعضاء البشرية.docx**: Course material in Arabic

### Interactive Presentations
- **01_Introduction_to_Anatomy.html**: Introduction to anatomical terminology and body organization
- **Human Anatomy & Physiology UST BME2025.html**: Complete course presentation covering all body systems

## Technical Implementation

### Web Technologies
**Frontend Framework**: Uses Tailwind CSS for styling
**JavaScript Libraries**: 
- Chart.js for data visualization
- Font Awesome for iconography

### Interactive Elements
**Animation Types**:
- Fade-in transitions
- Slide animations
- Pulse effects
- Interactive hover states

### Navigation System
**Features**:
- Slide navigation controls
- Progress tracking
- Responsive design for different screen sizes

## Course Content Structure

### Topics Covered
- Introduction to Anatomy & Physiology
- Body Organization Levels (Chemical to Organism)
- Major Body Systems:
  - Skeletal System
  - Muscular System
  - Respiratory System
  - Digestive System
  - Circulatory System
  - Nervous System
  - And more

### Learning Approach
- Visual learning with interactive diagrams
- System-based organization of content
- Integration of clinical contexts
- Progressive complexity from basic to advanced concepts

## Development Environment
**Editor Configuration**: VSCode settings available in .vscode directory
**Additional Tools**: Configuration for Zencoder in .zencoder directory