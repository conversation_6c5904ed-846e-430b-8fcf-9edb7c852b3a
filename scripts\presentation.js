(function(){
  // Progressive bullet reveal: press Space/Right to reveal next item inside .reveal lists
  const revealLists = Array.from(document.querySelectorAll('.reveal'));
  let revealIndexMap = new Map();
  revealLists.forEach((ul, idx) => {
    const items = Array.from(ul.querySelectorAll('li'));
    items.forEach(li => li.classList.remove('visible'));
    revealIndexMap.set(ul, 0);
  });

  function revealNext() {
    // find first list that still has hidden items
    for (const ul of revealLists) {
      const items = Array.from(ul.querySelectorAll('li'));
      const i = revealIndexMap.get(ul) ?? 0;
      if (i < items.length) {
        items[i].classList.add('visible');
        revealIndexMap.set(ul, i + 1);
        return true;
      }
    }
    return false;
  }

  // Quiz answer toggles
  document.addEventListener('click', (e)=>{
    const btn = e.target.closest('[data-toggle-answer]');
    if (btn) {
      const id = btn.getAttribute('data-toggle-answer');
      const ans = document.getElementById(id);
      if (ans) {
        const visible = ans.style.display === 'block';
        ans.style.display = visible ? 'none' : 'block';
      }
    }
  });

  // Keyboard navigation: Space/ArrowRight reveals next bullet; if none, go to next anchor with rel="next"
  document.addEventListener('keydown', (e)=>{
    if (e.key === ' ' || e.key === 'ArrowRight') {
      e.preventDefault();
      const revealed = revealNext();
      if (!revealed) {
        const next = document.querySelector('a[rel="next"]');
        if (next) window.location.href = next.href;
      }
    } else if (e.key === 'ArrowLeft') {
      const prev = document.querySelector('a[rel="prev"]');
      if (prev) window.location.href = prev.href;
    }
  });

  // Initialize: if any .reveal list exists, hide all items initially (handled above), else noop.

})();
